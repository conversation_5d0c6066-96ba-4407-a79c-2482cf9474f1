package com.bestpay.bigdata.bi.database.api.impl.subscribe;

import com.bestpay.bigdata.bi.database.api.subscribe.ObjectSubscribeService;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDTO;
import com.bestpay.bigdata.bi.database.bean.subscribe.SubDTO;
import com.bestpay.bigdata.bi.database.mapper.subscribe.ObjectSubscribeMapper;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: den<PERSON><PERSON><PERSON>
 * @date: 2022/12/20
 */
@Component
public class ObjectSubscribeServiceImpl implements ObjectSubscribeService {


    @Resource
    private ObjectSubscribeMapper objectSubscribeMapper;

    @Override
    public Long insert(ObjectSubScribeDO dashboardSubScribeDO) {
        objectSubscribeMapper.insert(dashboardSubScribeDO);
        return dashboardSubScribeDO.getId();
    }


    @Override
    public ObjectSubScribeDO getById(Long id) {
        return objectSubscribeMapper.getById(id);
    }

    @Override
    public ObjectSubScribeDO getByCode(String code) {
        return objectSubscribeMapper.getByCode(code);
    }


    @Override
    public int updateById(ObjectSubScribeDO dashboardSubScribeDO) {
        return objectSubscribeMapper.updateById(dashboardSubScribeDO);
    }

    @Override
    public int updateByDashboardId(ObjectSubScribeDO dashboardSubScribeDO) {
        return objectSubscribeMapper.updateByDashboardId(dashboardSubScribeDO);
    }

    @Override
    public List<ObjectSubScribeDO> queryAll() {
        return objectSubscribeMapper.queryAll();
    }

    @Override
    public List<ObjectSubScribeDO> queryByTaskName(String taskName,  Long id) {
        return objectSubscribeMapper.queryByTaskName(taskName,id);
    }

    @Override
    public List<ObjectSubScribeDO> queryByObjSubDTO(ObjectSubScribeDTO subScribeDTO) {

        return objectSubscribeMapper.queryByObjSubDTO(subScribeDTO);
    }

    @Override
    public String queryMaxSubCode() {
        return objectSubscribeMapper.queryMaxSubCode();
    }

    @Override
    public List<SubDTO> getSubscriptionTaskList(ObjectSubScribeDTO subScribeDTO) {
        List<SubDTO> result = objectSubscribeMapper.getSubscriptionTaskList(subScribeDTO);
        return result != null ? result : Collections.emptyList();
    }

    @Override
    public List<ObjectSubScribeDO> queryBySubType(String subType) {
        return objectSubscribeMapper.queryBySubType(subType);
    }

    @Override
    public List<ObjectSubScribeDO> findBak(ObjectSubScribeDTO objectSubscribeDTO) {
        return objectSubscribeMapper.findBak(objectSubscribeDTO);
    }

    @Override
    public void batchUpdateByIdBak(List<ObjectSubScribeDO> subscribeList) {
        if (subscribeList != null && !subscribeList.isEmpty()) {
            objectSubscribeMapper.batchUpdateByIdBak(subscribeList);
        }
    }

    @Override
    public void batchInsert(List<ObjectSubScribeDO> subscribeList) {
        if (subscribeList != null && !subscribeList.isEmpty()) {
            objectSubscribeMapper.batchInsert(subscribeList);
        }
    }
}
