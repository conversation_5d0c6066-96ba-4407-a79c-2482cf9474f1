package com.bestpay.bigdata.bi.database.api.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import java.util.List;

/**
 * ClassName: DashboardReportCardService
 * Package: com.bestpay.bigdata.bi.database.api.dashboard
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/10 13:37
 * @Version 1.0
 */
public interface DashboardReportCardService
{
    Long insert(DashboardReportCardDO cardDO);

    List<DashboardReportCardDO> find(ReportCardQueryDTO cardQuery);

    Long update(DashboardReportCardDO cardDO);

    void delete(Long dashboardId,String updateBy);

    void updateTableFontStyleAndTableConfigurationById(DashboardReportCardDO dashboardReportCardDO);

    List<DashboardReportCardDO> queryByChartTypes(List<Integer> chartTypeLis);

    int updateTableConfigurationById(DashboardReportCardDO toUpdate);

    // 新增备份表操作方法
    List<DashboardReportCardDO> findBak();

    void batchUpdateBak(List<DashboardReportCardDO> reportCardList);

    void batchInsert(List<DashboardReportCardDO> reportCardList);

}
