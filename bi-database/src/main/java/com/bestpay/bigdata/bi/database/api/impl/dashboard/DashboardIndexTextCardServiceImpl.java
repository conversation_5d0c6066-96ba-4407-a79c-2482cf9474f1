package com.bestpay.bigdata.bi.database.api.impl.dashboard;


import com.bestpay.bigdata.bi.common.dto.dashboard.IndexCardQueryDTO;
import com.bestpay.bigdata.bi.common.enums.IndexCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import com.bestpay.bigdata.bi.database.mapper.dashboard.DashboardIndexTextCardMapper;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
public class DashboardIndexTextCardServiceImpl implements DashboardIndexTextCardService {

    @Resource
    private DashboardIndexTextCardMapper indexTextCardMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(DashboardIndexTextCardDO filterCardDO){
        indexTextCardMapper.insert(filterCardDO);
        return filterCardDO.getId();
    }

    @Override
    public List<DashboardIndexTextCardDO> find(IndexCardQueryDTO cardQuery){
        return indexTextCardMapper.find(cardQuery);
    }

    @Override
    public Long update(DashboardIndexTextCardDO indexTextCardDO){
        indexTextCardMapper.update(indexTextCardDO);
        return indexTextCardDO.getId();
    }

    @Override
    public void delete(Long dashboardId,String updateBy){
        DashboardIndexTextCardDO delete = new DashboardIndexTextCardDO();
        delete.setDashboardId(dashboardId);
        delete.setIndexCardType(IndexCardTypeEnum.EMBED_DASHBOARD.getCode());
        delete.setStatusCode(StatusCodeEnum.DELETE.getCode());
        delete.setUpdatedAt(new Date());
        delete.setUpdatedBy(updateBy);
        indexTextCardMapper.update(delete);
    }

    @Override
    public List<DashboardIndexTextCardDO> queryByDashboardIdList(List<Long> dashboardIdList,String indexCardType) {

        return indexTextCardMapper.queryByDashboardIdList(dashboardIdList,indexCardType);
    }

    @Override
    public void batchInsert(List<DashboardIndexTextCardDO> dashboardIndexTextCardDOList) {
        indexTextCardMapper.batchInsert(dashboardIndexTextCardDOList);
    }

    @Override
    public List<DashboardIndexTextCardDO> findBak() {
        return indexTextCardMapper.findBak();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateBak(List<DashboardIndexTextCardDO> indexTextCardList) {
        if (indexTextCardList != null && !indexTextCardList.isEmpty()) {
            indexTextCardMapper.batchUpdateBak(indexTextCardList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<DashboardIndexTextCardDO> indexTextCardList) {
        if (indexTextCardList != null && !indexTextCardList.isEmpty()) {
            indexTextCardMapper.batchInsert(indexTextCardList);
        }
    }
}
